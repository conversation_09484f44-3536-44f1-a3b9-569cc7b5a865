{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./LiveMonitoringDashboard.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LiveMonitoringDashboard=()=>{var _profitData$trading_s,_profitData$trading_s2,_profitData$trading_s3,_profitData$trades;const[profitData,setProfitData]=useState(null);const[balanceData,setBalanceData]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[lastUpdate,setLastUpdate]=useState(new Date());const fetchData=async()=>{try{setError(null);// Fetch profit data\nconst profitResponse=await fetch('http://localhost:3205/profit');if(!profitResponse.ok)throw new Error('Failed to fetch profit data');const profitResult=await profitResponse.json();// Fetch balance data\nconst balanceResponse=await fetch('http://localhost:3205/balance');if(!balanceResponse.ok)throw new Error('Failed to fetch balance data');const balanceResult=await balanceResponse.json();setProfitData(profitResult);setBalanceData(balanceResult);setLastUpdate(new Date());setLoading(false);}catch(err){setError(err instanceof Error?err.message:'Unknown error');setLoading(false);}};useEffect(()=>{fetchData();const interval=setInterval(fetchData,10000);// Update every 10 seconds\nreturn()=>clearInterval(interval);},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"live-monitoring-dashboard\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loading\",children:\"\\uD83D\\uDD04 Loading live data...\"})});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"live-monitoring-dashboard\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"error\",children:[\"\\u274C Error: \",error]}),/*#__PURE__*/_jsx(\"button\",{onClick:fetchData,className:\"retry-button\",children:\"\\uD83D\\uDD04 Retry\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"live-monitoring-dashboard\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"dashboard-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83D\\uDCCA Live Trading Monitor\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"last-update\",children:[\"Last updated: \",lastUpdate.toLocaleTimeString()]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-cards\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"status-card profit\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-title\",children:\"\\uD83D\\uDCB0 Total Profit\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-value\",children:[\"$\",parseFloat((profitData===null||profitData===void 0?void 0:profitData.total_profit)||'0').toFixed(2)]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-subtitle\",children:[parseFloat((profitData===null||profitData===void 0?void 0:profitData.profit_percentage)||'0').toFixed(2),\"%\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-card portfolio\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-title\",children:\"\\uD83D\\uDCBC Portfolio Value\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-value\",children:[\"$\",parseFloat((balanceData===null||balanceData===void 0?void 0:balanceData.total_usd)||'0').toFixed(2)]}),/*#__PURE__*/_jsx(\"div\",{className:\"card-subtitle\",children:\"Real-time balance\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-card trades\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-title\",children:\"\\uD83D\\uDCC8 Total Trades\"}),/*#__PURE__*/_jsx(\"div\",{className:\"card-value\",children:(profitData===null||profitData===void 0?void 0:(_profitData$trading_s=profitData.trading_stats)===null||_profitData$trading_s===void 0?void 0:_profitData$trading_s.total_trades)||0}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-subtitle\",children:[(profitData===null||profitData===void 0?void 0:(_profitData$trading_s2=profitData.trading_stats)===null||_profitData$trading_s2===void 0?void 0:_profitData$trading_s2.open_positions)||0,\" open\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-card pnl\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-title\",children:\"\\u26A1 Unrealized P&L\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-value\",children:[\"$\",parseFloat((profitData===null||profitData===void 0?void 0:(_profitData$trading_s3=profitData.trading_stats)===null||_profitData$trading_s3===void 0?void 0:_profitData$trading_s3.unrealized_profit)||'0').toFixed(2)]}),/*#__PURE__*/_jsx(\"div\",{className:\"card-subtitle\",children:\"Live positions\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"recent-trades\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDD25 Recent Trades\"}),/*#__PURE__*/_jsx(\"div\",{className:\"trades-container\",children:profitData===null||profitData===void 0?void 0:(_profitData$trades=profitData.trades)===null||_profitData$trades===void 0?void 0:_profitData$trades.slice(0,10).map(trade=>/*#__PURE__*/_jsxs(\"div\",{className:`trade-item ${trade.side.toLowerCase()} ${trade.is_live_apollox?'live-apollox':''}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"trade-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"trade-symbol\",children:trade.symbol}),/*#__PURE__*/_jsx(\"span\",{className:`trade-side ${trade.side.toLowerCase()}`,children:trade.side}),trade.is_live_apollox&&/*#__PURE__*/_jsx(\"span\",{className:\"live-badge\",children:\"\\uD83D\\uDE80 LIVE\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"trade-details\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"trade-detail\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Amount:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"value\",children:parseFloat(trade.amount||'0').toFixed(4)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"trade-detail\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Price:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"value\",children:[\"$\",parseFloat(trade.price||'0').toFixed(4)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"trade-detail\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Value:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"value\",children:[\"$\",parseFloat(trade.trade_value||'0').toFixed(2)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"trade-detail\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"P&L:\"}),/*#__PURE__*/_jsxs(\"span\",{className:`value ${(trade.realized_pnl||0)+(trade.unrealized_pnl||0)>=0?'profit':'loss'}`,children:[\"$\",((trade.realized_pnl||0)+(trade.unrealized_pnl||0)).toFixed(2)]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"trade-timestamp\",children:new Date(trade.timestamp).toLocaleString()})]},trade.id))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-breakdown\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDCB0 Wallet Balance\"}),/*#__PURE__*/_jsx(\"div\",{className:\"balance-items\",children:balanceData&&Object.entries(balanceData.balances||{}).map(_ref=>{let[token,info]=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"balance-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"token-symbol\",children:token}),/*#__PURE__*/_jsx(\"div\",{className:\"token-amount\",children:parseFloat(info.balance||'0').toFixed(6)}),/*#__PURE__*/_jsxs(\"div\",{className:\"token-value\",children:[\"$\",parseFloat(info.usd_value||'0').toFixed(2)]})]},token);})})]})]});};export default LiveMonitoringDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "LiveMonitoringDashboard", "_profitData$trading_s", "_profitData$trading_s2", "_profitData$trading_s3", "_profitData$trades", "profitData", "setProfitData", "balanceData", "setBalanceData", "loading", "setLoading", "error", "setError", "lastUpdate", "setLastUpdate", "Date", "fetchData", "profitResponse", "fetch", "ok", "Error", "profitResult", "json", "balanceResponse", "balanceResult", "err", "message", "interval", "setInterval", "clearInterval", "className", "children", "onClick", "toLocaleTimeString", "parseFloat", "total_profit", "toFixed", "profit_percentage", "total_usd", "trading_stats", "total_trades", "open_positions", "unrealized_profit", "trades", "slice", "map", "trade", "side", "toLowerCase", "is_live_apollox", "symbol", "amount", "price", "trade_value", "realized_pnl", "unrealized_pnl", "timestamp", "toLocaleString", "id", "Object", "entries", "balances", "_ref", "token", "info", "balance", "usd_value"], "sources": ["/Users/<USER>/Projects/Total_AI_Liberation/dockerized_decentralized_agent/src/components/Widgets/LiveMonitoringDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './LiveMonitoringDashboard.css';\n\nconst LiveMonitoringDashboard = () => {\n  const [profitData, setProfitData] = useState(null);\n  const [balanceData, setBalanceData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(new Date());\n\n  const fetchData = async () => {\n    try {\n      setError(null);\n\n      // Fetch profit data\n      const profitResponse = await fetch('http://localhost:3205/profit');\n      if (!profitResponse.ok) throw new Error('Failed to fetch profit data');\n      const profitResult = await profitResponse.json();\n\n      // Fetch balance data\n      const balanceResponse = await fetch('http://localhost:3205/balance');\n      if (!balanceResponse.ok) throw new Error('Failed to fetch balance data');\n      const balanceResult = await balanceResponse.json();\n\n      setProfitData(profitResult);\n      setBalanceData(balanceResult);\n      setLastUpdate(new Date());\n      setLoading(false);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Unknown error');\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 10000); // Update every 10 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"live-monitoring-dashboard\">\n        <div className=\"loading\">🔄 Loading live data...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"live-monitoring-dashboard\">\n        <div className=\"error\">❌ Error: {error}</div>\n        <button onClick={fetchData} className=\"retry-button\">🔄 Retry</button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"live-monitoring-dashboard\">\n      <div className=\"dashboard-header\">\n        <h2>📊 Live Trading Monitor</h2>\n        <div className=\"last-update\">\n          Last updated: {lastUpdate.toLocaleTimeString()}\n        </div>\n      </div>\n\n      {/* Status Cards */}\n      <div className=\"status-cards\">\n        <div className=\"status-card profit\">\n          <div className=\"card-title\">💰 Total Profit</div>\n          <div className=\"card-value\">\n            ${parseFloat(profitData?.total_profit || '0').toFixed(2)}\n          </div>\n          <div className=\"card-subtitle\">\n            {parseFloat(profitData?.profit_percentage || '0').toFixed(2)}%\n          </div>\n        </div>\n\n        <div className=\"status-card portfolio\">\n          <div className=\"card-title\">💼 Portfolio Value</div>\n          <div className=\"card-value\">\n            ${parseFloat(balanceData?.total_usd || '0').toFixed(2)}\n          </div>\n          <div className=\"card-subtitle\">Real-time balance</div>\n        </div>\n\n        <div className=\"status-card trades\">\n          <div className=\"card-title\">📈 Total Trades</div>\n          <div className=\"card-value\">\n            {profitData?.trading_stats?.total_trades || 0}\n          </div>\n          <div className=\"card-subtitle\">\n            {profitData?.trading_stats?.open_positions || 0} open\n          </div>\n        </div>\n\n        <div className=\"status-card pnl\">\n          <div className=\"card-title\">⚡ Unrealized P&L</div>\n          <div className=\"card-value\">\n            ${parseFloat(profitData?.trading_stats?.unrealized_profit || '0').toFixed(2)}\n          </div>\n          <div className=\"card-subtitle\">Live positions</div>\n        </div>\n      </div>\n\n      {/* Recent Trades */}\n      <div className=\"recent-trades\">\n        <h3>🔥 Recent Trades</h3>\n        <div className=\"trades-container\">\n          {profitData?.trades?.slice(0, 10).map((trade) => (\n            <div key={trade.id} className={`trade-item ${trade.side.toLowerCase()} ${trade.is_live_apollox ? 'live-apollox' : ''}`}>\n              <div className=\"trade-header\">\n                <span className=\"trade-symbol\">{trade.symbol}</span>\n                <span className={`trade-side ${trade.side.toLowerCase()}`}>\n                  {trade.side}\n                </span>\n                {trade.is_live_apollox && (\n                  <span className=\"live-badge\">🚀 LIVE</span>\n                )}\n              </div>\n              <div className=\"trade-details\">\n                <div className=\"trade-detail\">\n                  <span className=\"label\">Amount:</span>\n                  <span className=\"value\">{parseFloat(trade.amount || '0').toFixed(4)}</span>\n                </div>\n                <div className=\"trade-detail\">\n                  <span className=\"label\">Price:</span>\n                  <span className=\"value\">${parseFloat(trade.price || '0').toFixed(4)}</span>\n                </div>\n                <div className=\"trade-detail\">\n                  <span className=\"label\">Value:</span>\n                  <span className=\"value\">${parseFloat(trade.trade_value || '0').toFixed(2)}</span>\n                </div>\n                <div className=\"trade-detail\">\n                  <span className=\"label\">P&L:</span>\n                  <span className={`value ${((trade.realized_pnl || 0) + (trade.unrealized_pnl || 0)) >= 0 ? 'profit' : 'loss'}`}>\n                    ${((trade.realized_pnl || 0) + (trade.unrealized_pnl || 0)).toFixed(2)}\n                  </span>\n                </div>\n              </div>\n              <div className=\"trade-timestamp\">\n                {new Date(trade.timestamp).toLocaleString()}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Balance Breakdown */}\n      <div className=\"balance-breakdown\">\n        <h3>💰 Wallet Balance</h3>\n        <div className=\"balance-items\">\n          {balanceData && Object.entries(balanceData.balances || {}).map(([token, info]) => (\n            <div key={token} className=\"balance-item\">\n              <div className=\"token-symbol\">{token}</div>\n              <div className=\"token-amount\">{parseFloat(info.balance || '0').toFixed(6)}</div>\n              <div className=\"token-value\">${parseFloat(info.usd_value || '0').toFixed(2)}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LiveMonitoringDashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,uBAAuB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CACpC,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACa,WAAW,CAAEC,cAAc,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACe,OAAO,CAAEC,UAAU,CAAC,CAAGhB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACmB,UAAU,CAAEC,aAAa,CAAC,CAAGpB,QAAQ,CAAC,GAAI,CAAAqB,IAAI,CAAC,CAAC,CAAC,CAExD,KAAM,CAAAC,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACFJ,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAAK,cAAc,CAAG,KAAM,CAAAC,KAAK,CAAC,8BAA8B,CAAC,CAClE,GAAI,CAACD,cAAc,CAACE,EAAE,CAAE,KAAM,IAAI,CAAAC,KAAK,CAAC,6BAA6B,CAAC,CACtE,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAAJ,cAAc,CAACK,IAAI,CAAC,CAAC,CAEhD;AACA,KAAM,CAAAC,eAAe,CAAG,KAAM,CAAAL,KAAK,CAAC,+BAA+B,CAAC,CACpE,GAAI,CAACK,eAAe,CAACJ,EAAE,CAAE,KAAM,IAAI,CAAAC,KAAK,CAAC,8BAA8B,CAAC,CACxE,KAAM,CAAAI,aAAa,CAAG,KAAM,CAAAD,eAAe,CAACD,IAAI,CAAC,CAAC,CAElDhB,aAAa,CAACe,YAAY,CAAC,CAC3Bb,cAAc,CAACgB,aAAa,CAAC,CAC7BV,aAAa,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,CACzBL,UAAU,CAAC,KAAK,CAAC,CACnB,CAAE,MAAOe,GAAG,CAAE,CACZb,QAAQ,CAACa,GAAG,WAAY,CAAAL,KAAK,CAAGK,GAAG,CAACC,OAAO,CAAG,eAAe,CAAC,CAC9DhB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDf,SAAS,CAAC,IAAM,CACdqB,SAAS,CAAC,CAAC,CACX,KAAM,CAAAW,QAAQ,CAAGC,WAAW,CAACZ,SAAS,CAAE,KAAK,CAAC,CAAE;AAChD,MAAO,IAAMa,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIlB,OAAO,CAAE,CACX,mBACEZ,IAAA,QAAKiC,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxClC,IAAA,QAAKiC,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,mCAAuB,CAAK,CAAC,CACnD,CAAC,CAEV,CAEA,GAAIpB,KAAK,CAAE,CACT,mBACEZ,KAAA,QAAK+B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxChC,KAAA,QAAK+B,SAAS,CAAC,OAAO,CAAAC,QAAA,EAAC,gBAAS,CAACpB,KAAK,EAAM,CAAC,cAC7Cd,IAAA,WAAQmC,OAAO,CAAEhB,SAAU,CAACc,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,oBAAQ,CAAQ,CAAC,EACnE,CAAC,CAEV,CAEA,mBACEhC,KAAA,QAAK+B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxChC,KAAA,QAAK+B,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BlC,IAAA,OAAAkC,QAAA,CAAI,mCAAuB,CAAI,CAAC,cAChChC,KAAA,QAAK+B,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,gBACb,CAAClB,UAAU,CAACoB,kBAAkB,CAAC,CAAC,EAC3C,CAAC,EACH,CAAC,cAGNlC,KAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhC,KAAA,QAAK+B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjClC,IAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,2BAAe,CAAK,CAAC,cACjDhC,KAAA,QAAK+B,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,GACzB,CAACG,UAAU,CAAC,CAAA7B,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE8B,YAAY,GAAI,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EACrD,CAAC,cACNrC,KAAA,QAAK+B,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3BG,UAAU,CAAC,CAAA7B,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEgC,iBAAiB,GAAI,GAAG,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,CAAC,GAC/D,EAAK,CAAC,EACH,CAAC,cAENrC,KAAA,QAAK+B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpClC,IAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,8BAAkB,CAAK,CAAC,cACpDhC,KAAA,QAAK+B,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,GACzB,CAACG,UAAU,CAAC,CAAA3B,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE+B,SAAS,GAAI,GAAG,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,EACnD,CAAC,cACNvC,IAAA,QAAKiC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mBAAiB,CAAK,CAAC,EACnD,CAAC,cAENhC,KAAA,QAAK+B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjClC,IAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,2BAAe,CAAK,CAAC,cACjDlC,IAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxB,CAAA1B,UAAU,SAAVA,UAAU,kBAAAJ,qBAAA,CAAVI,UAAU,CAAEkC,aAAa,UAAAtC,qBAAA,iBAAzBA,qBAAA,CAA2BuC,YAAY,GAAI,CAAC,CAC1C,CAAC,cACNzC,KAAA,QAAK+B,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3B,CAAA1B,UAAU,SAAVA,UAAU,kBAAAH,sBAAA,CAAVG,UAAU,CAAEkC,aAAa,UAAArC,sBAAA,iBAAzBA,sBAAA,CAA2BuC,cAAc,GAAI,CAAC,CAAC,OAClD,EAAK,CAAC,EACH,CAAC,cAEN1C,KAAA,QAAK+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BlC,IAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,uBAAgB,CAAK,CAAC,cAClDhC,KAAA,QAAK+B,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,GACzB,CAACG,UAAU,CAAC,CAAA7B,UAAU,SAAVA,UAAU,kBAAAF,sBAAA,CAAVE,UAAU,CAAEkC,aAAa,UAAApC,sBAAA,iBAAzBA,sBAAA,CAA2BuC,iBAAiB,GAAI,GAAG,CAAC,CAACN,OAAO,CAAC,CAAC,CAAC,EACzE,CAAC,cACNvC,IAAA,QAAKiC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gBAAc,CAAK,CAAC,EAChD,CAAC,EACH,CAAC,cAGNhC,KAAA,QAAK+B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BlC,IAAA,OAAAkC,QAAA,CAAI,4BAAgB,CAAI,CAAC,cACzBlC,IAAA,QAAKiC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9B1B,UAAU,SAAVA,UAAU,kBAAAD,kBAAA,CAAVC,UAAU,CAAEsC,MAAM,UAAAvC,kBAAA,iBAAlBA,kBAAA,CAAoBwC,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAACC,GAAG,CAAEC,KAAK,eAC1C/C,KAAA,QAAoB+B,SAAS,CAAE,cAAcgB,KAAK,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,IAAIF,KAAK,CAACG,eAAe,CAAG,cAAc,CAAG,EAAE,EAAG,CAAAlB,QAAA,eACrHhC,KAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BlC,IAAA,SAAMiC,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEe,KAAK,CAACI,MAAM,CAAO,CAAC,cACpDrD,IAAA,SAAMiC,SAAS,CAAE,cAAcgB,KAAK,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAG,CAAAjB,QAAA,CACvDe,KAAK,CAACC,IAAI,CACP,CAAC,CACND,KAAK,CAACG,eAAe,eACpBpD,IAAA,SAAMiC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,mBAAO,CAAM,CAC3C,EACE,CAAC,cACNhC,KAAA,QAAK+B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhC,KAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BlC,IAAA,SAAMiC,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cACtClC,IAAA,SAAMiC,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAEG,UAAU,CAACY,KAAK,CAACK,MAAM,EAAI,GAAG,CAAC,CAACf,OAAO,CAAC,CAAC,CAAC,CAAO,CAAC,EACxE,CAAC,cACNrC,KAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BlC,IAAA,SAAMiC,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cACrChC,KAAA,SAAM+B,SAAS,CAAC,OAAO,CAAAC,QAAA,EAAC,GAAC,CAACG,UAAU,CAACY,KAAK,CAACM,KAAK,EAAI,GAAG,CAAC,CAAChB,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EACxE,CAAC,cACNrC,KAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BlC,IAAA,SAAMiC,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cACrChC,KAAA,SAAM+B,SAAS,CAAC,OAAO,CAAAC,QAAA,EAAC,GAAC,CAACG,UAAU,CAACY,KAAK,CAACO,WAAW,EAAI,GAAG,CAAC,CAACjB,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EAC9E,CAAC,cACNrC,KAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BlC,IAAA,SAAMiC,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,MAAI,CAAM,CAAC,cACnChC,KAAA,SAAM+B,SAAS,CAAE,SAAU,CAACgB,KAAK,CAACQ,YAAY,EAAI,CAAC,GAAKR,KAAK,CAACS,cAAc,EAAI,CAAC,CAAC,EAAK,CAAC,CAAG,QAAQ,CAAG,MAAM,EAAG,CAAAxB,QAAA,EAAC,GAC7G,CAAC,CAAC,CAACe,KAAK,CAACQ,YAAY,EAAI,CAAC,GAAKR,KAAK,CAACS,cAAc,EAAI,CAAC,CAAC,EAAEnB,OAAO,CAAC,CAAC,CAAC,EAClE,CAAC,EACJ,CAAC,EACH,CAAC,cACNvC,IAAA,QAAKiC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7B,GAAI,CAAAhB,IAAI,CAAC+B,KAAK,CAACU,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC,CACxC,CAAC,GAhCEX,KAAK,CAACY,EAiCX,CACN,CAAC,CACC,CAAC,EACH,CAAC,cAGN3D,KAAA,QAAK+B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClC,IAAA,OAAAkC,QAAA,CAAI,6BAAiB,CAAI,CAAC,cAC1BlC,IAAA,QAAKiC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BxB,WAAW,EAAIoD,MAAM,CAACC,OAAO,CAACrD,WAAW,CAACsD,QAAQ,EAAI,CAAC,CAAC,CAAC,CAAChB,GAAG,CAACiB,IAAA,MAAC,CAACC,KAAK,CAAEC,IAAI,CAAC,CAAAF,IAAA,oBAC3E/D,KAAA,QAAiB+B,SAAS,CAAC,cAAc,CAAAC,QAAA,eACvClC,IAAA,QAAKiC,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEgC,KAAK,CAAM,CAAC,cAC3ClE,IAAA,QAAKiC,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEG,UAAU,CAAC8B,IAAI,CAACC,OAAO,EAAI,GAAG,CAAC,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAAM,CAAC,cAChFrC,KAAA,QAAK+B,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,GAAC,CAACG,UAAU,CAAC8B,IAAI,CAACE,SAAS,EAAI,GAAG,CAAC,CAAC9B,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,GAH1E2B,KAIL,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA/D,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}