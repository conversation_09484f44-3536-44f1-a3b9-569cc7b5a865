{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Projects/Total_AI_Liberation/dockerized_decentralized_agent/src/components/Widgets/LiveMonitoringDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './LiveMonitoringDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LiveMonitoringDashboard = () => {\n  _s();\n  var _profitData$trading_s, _profitData$trading_s2, _profitData$trading_s3, _profitData$trades;\n  const [profitData, setProfitData] = useState(null);\n  const [balanceData, setBalanceData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(new Date());\n  const fetchData = async () => {\n    try {\n      setError(null);\n\n      // Fetch profit data\n      const profitResponse = await fetch('http://localhost:3205/profit');\n      if (!profitResponse.ok) throw new Error('Failed to fetch profit data');\n      const profitResult = await profitResponse.json();\n\n      // Fetch balance data\n      const balanceResponse = await fetch('http://localhost:3205/balance');\n      if (!balanceResponse.ok) throw new Error('Failed to fetch balance data');\n      const balanceResult = await balanceResponse.json();\n      setProfitData(profitResult);\n      setBalanceData(balanceResult);\n      setLastUpdate(new Date());\n      setLoading(false);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Unknown error');\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 10000); // Update every 10 seconds\n    return () => clearInterval(interval);\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"live-monitoring-dashboard\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"\\uD83D\\uDD04 Loading live data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"live-monitoring-dashboard\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u274C Error: \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchData,\n        className: \"retry-button\",\n        children: \"\\uD83D\\uDD04 Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"live-monitoring-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\uD83D\\uDCCA Live Trading Monitor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"last-update\",\n        children: [\"Last updated: \", lastUpdate.toLocaleTimeString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"status-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-card profit\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-title\",\n          children: \"\\uD83D\\uDCB0 Total Profit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-value\",\n          children: [\"$\", parseFloat((profitData === null || profitData === void 0 ? void 0 : profitData.total_profit) || '0').toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-subtitle\",\n          children: [parseFloat((profitData === null || profitData === void 0 ? void 0 : profitData.profit_percentage) || '0').toFixed(2), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-card portfolio\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-title\",\n          children: \"\\uD83D\\uDCBC Portfolio Value\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-value\",\n          children: [\"$\", parseFloat((balanceData === null || balanceData === void 0 ? void 0 : balanceData.total_usd) || '0').toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-subtitle\",\n          children: \"Real-time balance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-card trades\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-title\",\n          children: \"\\uD83D\\uDCC8 Total Trades\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-value\",\n          children: (profitData === null || profitData === void 0 ? void 0 : (_profitData$trading_s = profitData.trading_stats) === null || _profitData$trading_s === void 0 ? void 0 : _profitData$trading_s.total_trades) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-subtitle\",\n          children: [(profitData === null || profitData === void 0 ? void 0 : (_profitData$trading_s2 = profitData.trading_stats) === null || _profitData$trading_s2 === void 0 ? void 0 : _profitData$trading_s2.open_positions) || 0, \" open\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-card pnl\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-title\",\n          children: \"\\u26A1 Unrealized P&L\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-value\",\n          children: [\"$\", parseFloat((profitData === null || profitData === void 0 ? void 0 : (_profitData$trading_s3 = profitData.trading_stats) === null || _profitData$trading_s3 === void 0 ? void 0 : _profitData$trading_s3.unrealized_profit) || '0').toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-subtitle\",\n          children: \"Live positions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"recent-trades\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDD25 Recent Trades\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"trades-container\",\n        children: profitData === null || profitData === void 0 ? void 0 : (_profitData$trades = profitData.trades) === null || _profitData$trades === void 0 ? void 0 : _profitData$trades.slice(0, 10).map((trade, index) => {\n          var _trade$side, _trade$side2;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `trade-item ${((_trade$side = trade.side) === null || _trade$side === void 0 ? void 0 : _trade$side.toLowerCase()) || ''} ${trade.is_live_apollox ? 'live-apollox' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"trade-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"trade-symbol\",\n                children: trade.symbol\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `trade-side ${((_trade$side2 = trade.side) === null || _trade$side2 === void 0 ? void 0 : _trade$side2.toLowerCase()) || ''}`,\n                children: trade.side || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), trade.is_live_apollox && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"live-badge\",\n                children: \"\\uD83D\\uDE80 LIVE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"trade-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trade-detail\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Amount:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: parseFloat(trade.amount || '0').toFixed(4)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trade-detail\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Price:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: [\"$\", parseFloat(trade.price || '0').toFixed(4)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trade-detail\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Value:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: [\"$\", parseFloat(trade.trade_value || '0').toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trade-detail\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"P&L:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `value ${(parseFloat(trade.realized_pnl) || 0) + (parseFloat(trade.unrealized_pnl) || 0) >= 0 ? 'profit' : 'loss'}`,\n                  children: [\"$\", ((parseFloat(trade.realized_pnl) || 0) + (parseFloat(trade.unrealized_pnl) || 0)).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"trade-timestamp\",\n              children: new Date(trade.timestamp).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, trade.id || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"balance-breakdown\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDCB0 Wallet Balance\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"balance-items\",\n        children: balanceData && Object.entries(balanceData.balances || {}).map(([token, info]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"balance-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-symbol\",\n            children: token\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-amount\",\n            children: parseFloat(info.balance || '0').toFixed(6)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-value\",\n            children: [\"$\", parseFloat(info.usd_value || '0').toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this)]\n        }, token, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(LiveMonitoringDashboard, \"U3Sk++q7/BS+WLEsq+vaqhhC1Ig=\");\n_c = LiveMonitoringDashboard;\nexport default LiveMonitoringDashboard;\nvar _c;\n$RefreshReg$(_c, \"LiveMonitoringDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "LiveMonitoringDashboard", "_s", "_profitData$trading_s", "_profitData$trading_s2", "_profitData$trading_s3", "_profitData$trades", "profitData", "setProfitData", "balanceData", "setBalanceData", "loading", "setLoading", "error", "setError", "lastUpdate", "setLastUpdate", "Date", "fetchData", "profitResponse", "fetch", "ok", "Error", "profitResult", "json", "balanceResponse", "balanceResult", "err", "message", "interval", "setInterval", "clearInterval", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "toLocaleTimeString", "parseFloat", "total_profit", "toFixed", "profit_percentage", "total_usd", "trading_stats", "total_trades", "open_positions", "unrealized_profit", "trades", "slice", "map", "trade", "index", "_trade$side", "_trade$side2", "side", "toLowerCase", "is_live_apollox", "symbol", "amount", "price", "trade_value", "realized_pnl", "unrealized_pnl", "timestamp", "toLocaleString", "id", "Object", "entries", "balances", "token", "info", "balance", "usd_value", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Projects/Total_AI_Liberation/dockerized_decentralized_agent/src/components/Widgets/LiveMonitoringDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './LiveMonitoringDashboard.css';\n\nconst LiveMonitoringDashboard = () => {\n  const [profitData, setProfitData] = useState(null);\n  const [balanceData, setBalanceData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(new Date());\n\n  const fetchData = async () => {\n    try {\n      setError(null);\n\n      // Fetch profit data\n      const profitResponse = await fetch('http://localhost:3205/profit');\n      if (!profitResponse.ok) throw new Error('Failed to fetch profit data');\n      const profitResult = await profitResponse.json();\n\n      // Fetch balance data\n      const balanceResponse = await fetch('http://localhost:3205/balance');\n      if (!balanceResponse.ok) throw new Error('Failed to fetch balance data');\n      const balanceResult = await balanceResponse.json();\n\n      setProfitData(profitResult);\n      setBalanceData(balanceResult);\n      setLastUpdate(new Date());\n      setLoading(false);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Unknown error');\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 10000); // Update every 10 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"live-monitoring-dashboard\">\n        <div className=\"loading\">🔄 Loading live data...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"live-monitoring-dashboard\">\n        <div className=\"error\">❌ Error: {error}</div>\n        <button onClick={fetchData} className=\"retry-button\">🔄 Retry</button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"live-monitoring-dashboard\">\n      <div className=\"dashboard-header\">\n        <h2>📊 Live Trading Monitor</h2>\n        <div className=\"last-update\">\n          Last updated: {lastUpdate.toLocaleTimeString()}\n        </div>\n      </div>\n\n      {/* Status Cards */}\n      <div className=\"status-cards\">\n        <div className=\"status-card profit\">\n          <div className=\"card-title\">💰 Total Profit</div>\n          <div className=\"card-value\">\n            ${parseFloat(profitData?.total_profit || '0').toFixed(2)}\n          </div>\n          <div className=\"card-subtitle\">\n            {parseFloat(profitData?.profit_percentage || '0').toFixed(2)}%\n          </div>\n        </div>\n\n        <div className=\"status-card portfolio\">\n          <div className=\"card-title\">💼 Portfolio Value</div>\n          <div className=\"card-value\">\n            ${parseFloat(balanceData?.total_usd || '0').toFixed(2)}\n          </div>\n          <div className=\"card-subtitle\">Real-time balance</div>\n        </div>\n\n        <div className=\"status-card trades\">\n          <div className=\"card-title\">📈 Total Trades</div>\n          <div className=\"card-value\">\n            {profitData?.trading_stats?.total_trades || 0}\n          </div>\n          <div className=\"card-subtitle\">\n            {profitData?.trading_stats?.open_positions || 0} open\n          </div>\n        </div>\n\n        <div className=\"status-card pnl\">\n          <div className=\"card-title\">⚡ Unrealized P&L</div>\n          <div className=\"card-value\">\n            ${parseFloat(profitData?.trading_stats?.unrealized_profit || '0').toFixed(2)}\n          </div>\n          <div className=\"card-subtitle\">Live positions</div>\n        </div>\n      </div>\n\n      {/* Recent Trades */}\n      <div className=\"recent-trades\">\n        <h3>🔥 Recent Trades</h3>\n        <div className=\"trades-container\">\n          {profitData?.trades?.slice(0, 10).map((trade, index) => (\n            <div key={trade.id || index} className={`trade-item ${trade.side?.toLowerCase() || ''} ${trade.is_live_apollox ? 'live-apollox' : ''}`}>\n              <div className=\"trade-header\">\n                <span className=\"trade-symbol\">{trade.symbol}</span>\n                <span className={`trade-side ${trade.side?.toLowerCase() || ''}`}>\n                  {trade.side || 'N/A'}\n                </span>\n                {trade.is_live_apollox && (\n                  <span className=\"live-badge\">🚀 LIVE</span>\n                )}\n              </div>\n              <div className=\"trade-details\">\n                <div className=\"trade-detail\">\n                  <span className=\"label\">Amount:</span>\n                  <span className=\"value\">{parseFloat(trade.amount || '0').toFixed(4)}</span>\n                </div>\n                <div className=\"trade-detail\">\n                  <span className=\"label\">Price:</span>\n                  <span className=\"value\">${parseFloat(trade.price || '0').toFixed(4)}</span>\n                </div>\n                <div className=\"trade-detail\">\n                  <span className=\"label\">Value:</span>\n                  <span className=\"value\">${parseFloat(trade.trade_value || '0').toFixed(2)}</span>\n                </div>\n                <div className=\"trade-detail\">\n                  <span className=\"label\">P&L:</span>\n                  <span className={`value ${((parseFloat(trade.realized_pnl) || 0) + (parseFloat(trade.unrealized_pnl) || 0)) >= 0 ? 'profit' : 'loss'}`}>\n                    ${((parseFloat(trade.realized_pnl) || 0) + (parseFloat(trade.unrealized_pnl) || 0)).toFixed(2)}\n                  </span>\n                </div>\n              </div>\n              <div className=\"trade-timestamp\">\n                {new Date(trade.timestamp).toLocaleString()}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Balance Breakdown */}\n      <div className=\"balance-breakdown\">\n        <h3>💰 Wallet Balance</h3>\n        <div className=\"balance-items\">\n          {balanceData && Object.entries(balanceData.balances || {}).map(([token, info]) => (\n            <div key={token} className=\"balance-item\">\n              <div className=\"token-symbol\">{token}</div>\n              <div className=\"token-amount\">{parseFloat(info.balance || '0').toFixed(6)}</div>\n              <div className=\"token-value\">${parseFloat(info.usd_value || '0').toFixed(2)}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LiveMonitoringDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,kBAAA;EACpC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,IAAIoB,IAAI,CAAC,CAAC,CAAC;EAExD,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFJ,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMK,cAAc,GAAG,MAAMC,KAAK,CAAC,8BAA8B,CAAC;MAClE,IAAI,CAACD,cAAc,CAACE,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;MACtE,MAAMC,YAAY,GAAG,MAAMJ,cAAc,CAACK,IAAI,CAAC,CAAC;;MAEhD;MACA,MAAMC,eAAe,GAAG,MAAML,KAAK,CAAC,+BAA+B,CAAC;MACpE,IAAI,CAACK,eAAe,CAACJ,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACxE,MAAMI,aAAa,GAAG,MAAMD,eAAe,CAACD,IAAI,CAAC,CAAC;MAElDhB,aAAa,CAACe,YAAY,CAAC;MAC3Bb,cAAc,CAACgB,aAAa,CAAC;MAC7BV,aAAa,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;MACzBL,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOe,GAAG,EAAE;MACZb,QAAQ,CAACa,GAAG,YAAYL,KAAK,GAAGK,GAAG,CAACC,OAAO,GAAG,eAAe,CAAC;MAC9DhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDd,SAAS,CAAC,MAAM;IACdoB,SAAS,CAAC,CAAC;IACX,MAAMW,QAAQ,GAAGC,WAAW,CAACZ,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IAChD,OAAO,MAAMa,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIlB,OAAO,EAAE;IACX,oBACEX,OAAA;MAAKgC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxCjC,OAAA;QAAKgC,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAEV;EAEA,IAAIxB,KAAK,EAAE;IACT,oBACEb,OAAA;MAAKgC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxCjC,OAAA;QAAKgC,SAAS,EAAC,OAAO;QAAAC,QAAA,GAAC,gBAAS,EAACpB,KAAK;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7CrC,OAAA;QAAQsC,OAAO,EAAEpB,SAAU;QAACc,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC;EAEV;EAEA,oBACErC,OAAA;IAAKgC,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCjC,OAAA;MAAKgC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BjC,OAAA;QAAAiC,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCrC,OAAA;QAAKgC,SAAS,EAAC,aAAa;QAAAC,QAAA,GAAC,gBACb,EAAClB,UAAU,CAACwB,kBAAkB,CAAC,CAAC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BjC,OAAA;QAAKgC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCjC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjDrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,GACzB,EAACO,UAAU,CAAC,CAAAjC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkC,YAAY,KAAI,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BO,UAAU,CAAC,CAAAjC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoC,iBAAiB,KAAI,GAAG,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,GAC/D;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrC,OAAA;QAAKgC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCjC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,GACzB,EAACO,UAAU,CAAC,CAAA/B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmC,SAAS,KAAI,GAAG,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENrC,OAAA;QAAKgC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCjC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjDrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB,CAAA1B,UAAU,aAAVA,UAAU,wBAAAJ,qBAAA,GAAVI,UAAU,CAAEsC,aAAa,cAAA1C,qBAAA,uBAAzBA,qBAAA,CAA2B2C,YAAY,KAAI;QAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3B,CAAA1B,UAAU,aAAVA,UAAU,wBAAAH,sBAAA,GAAVG,UAAU,CAAEsC,aAAa,cAAAzC,sBAAA,uBAAzBA,sBAAA,CAA2B2C,cAAc,KAAI,CAAC,EAAC,OAClD;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrC,OAAA;QAAKgC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BjC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,GACzB,EAACO,UAAU,CAAC,CAAAjC,UAAU,aAAVA,UAAU,wBAAAF,sBAAA,GAAVE,UAAU,CAAEsC,aAAa,cAAAxC,sBAAA,uBAAzBA,sBAAA,CAA2B2C,iBAAiB,KAAI,GAAG,CAAC,CAACN,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BjC,OAAA;QAAAiC,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzBrC,OAAA;QAAKgC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAC9B1B,UAAU,aAAVA,UAAU,wBAAAD,kBAAA,GAAVC,UAAU,CAAE0C,MAAM,cAAA3C,kBAAA,uBAAlBA,kBAAA,CAAoB4C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK;UAAA,IAAAC,WAAA,EAAAC,YAAA;UAAA,oBACjDvD,OAAA;YAA6BgC,SAAS,EAAE,cAAc,EAAAsB,WAAA,GAAAF,KAAK,CAACI,IAAI,cAAAF,WAAA,uBAAVA,WAAA,CAAYG,WAAW,CAAC,CAAC,KAAI,EAAE,IAAIL,KAAK,CAACM,eAAe,GAAG,cAAc,GAAG,EAAE,EAAG;YAAAzB,QAAA,gBACrIjC,OAAA;cAAKgC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BjC,OAAA;gBAAMgC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEmB,KAAK,CAACO;cAAM;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDrC,OAAA;gBAAMgC,SAAS,EAAE,cAAc,EAAAuB,YAAA,GAAAH,KAAK,CAACI,IAAI,cAAAD,YAAA,uBAAVA,YAAA,CAAYE,WAAW,CAAC,CAAC,KAAI,EAAE,EAAG;gBAAAxB,QAAA,EAC9DmB,KAAK,CAACI,IAAI,IAAI;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,EACNe,KAAK,CAACM,eAAe,iBACpB1D,OAAA;gBAAMgC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BjC,OAAA;gBAAKgC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BjC,OAAA;kBAAMgC,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCrC,OAAA;kBAAMgC,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAEO,UAAU,CAACY,KAAK,CAACQ,MAAM,IAAI,GAAG,CAAC,CAAClB,OAAO,CAAC,CAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACNrC,OAAA;gBAAKgC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BjC,OAAA;kBAAMgC,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrCrC,OAAA;kBAAMgC,SAAS,EAAC,OAAO;kBAAAC,QAAA,GAAC,GAAC,EAACO,UAAU,CAACY,KAAK,CAACS,KAAK,IAAI,GAAG,CAAC,CAACnB,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACNrC,OAAA;gBAAKgC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BjC,OAAA;kBAAMgC,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrCrC,OAAA;kBAAMgC,SAAS,EAAC,OAAO;kBAAAC,QAAA,GAAC,GAAC,EAACO,UAAU,CAACY,KAAK,CAACU,WAAW,IAAI,GAAG,CAAC,CAACpB,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNrC,OAAA;gBAAKgC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BjC,OAAA;kBAAMgC,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnCrC,OAAA;kBAAMgC,SAAS,EAAE,SAAU,CAACQ,UAAU,CAACY,KAAK,CAACW,YAAY,CAAC,IAAI,CAAC,KAAKvB,UAAU,CAACY,KAAK,CAACY,cAAc,CAAC,IAAI,CAAC,CAAC,IAAK,CAAC,GAAG,QAAQ,GAAG,MAAM,EAAG;kBAAA/B,QAAA,GAAC,GACrI,EAAC,CAAC,CAACO,UAAU,CAACY,KAAK,CAACW,YAAY,CAAC,IAAI,CAAC,KAAKvB,UAAU,CAACY,KAAK,CAACY,cAAc,CAAC,IAAI,CAAC,CAAC,EAAEtB,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7B,IAAIhB,IAAI,CAACmC,KAAK,CAACa,SAAS,CAAC,CAACC,cAAc,CAAC;YAAC;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA,GAhCEe,KAAK,CAACe,EAAE,IAAId,KAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiCtB,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCjC,OAAA;QAAAiC,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BrC,OAAA;QAAKgC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BxB,WAAW,IAAI2D,MAAM,CAACC,OAAO,CAAC5D,WAAW,CAAC6D,QAAQ,IAAI,CAAC,CAAC,CAAC,CAACnB,GAAG,CAAC,CAAC,CAACoB,KAAK,EAAEC,IAAI,CAAC,kBAC3ExE,OAAA;UAAiBgC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACvCjC,OAAA;YAAKgC,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEsC;UAAK;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3CrC,OAAA;YAAKgC,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEO,UAAU,CAACgC,IAAI,CAACC,OAAO,IAAI,GAAG,CAAC,CAAC/B,OAAO,CAAC,CAAC;UAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChFrC,OAAA;YAAKgC,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,GAAC,EAACO,UAAU,CAACgC,IAAI,CAACE,SAAS,IAAI,GAAG,CAAC,CAAChC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAH1EkC,KAAK;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CAhKID,uBAAuB;AAAA0E,EAAA,GAAvB1E,uBAAuB;AAkK7B,eAAeA,uBAAuB;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}