import React, { useState, useEffect } from 'react';
import TradingControlPanel from './components/Widgets/TradingControlPanel';
import LiveMonitoringDashboard from './components/Widgets/LiveMonitoringDashboard';
import './App.css';

const App = () => {
  const [appState, setAppState] = useState({
    isTrading: false,
    systemStatus: 'healthy',
    lastUpdate: new Date()
  });

  const [notifications, setNotifications] = useState([]);

  const addNotification = (message) => {
    setNotifications(prev => [message, ...prev.slice(0, 4)]); // Keep only 5 notifications
    setTimeout(() => {
      setNotifications(prev => prev.slice(0, -1));
    }, 5000);
  };

  const handleStartTrading = async () => {
    try {
      addNotification('🚀 Starting trading system...');

      // Call the correct backend endpoint to start trading
      const response = await fetch('http://localhost:3205/trading-loop/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: 'BNBUSD',  // Your current winning trade!
          mode: 'aggressive',
          auto_execute: true,
          profit_target: 1.0,
          max_loss: 0.5
        })
      });

      if (response.ok) {
        setAppState(prev => ({ ...prev, isTrading: true, lastUpdate: new Date() }));
        addNotification('✅ Trading system started successfully!');
      } else {
        addNotification('❌ Failed to start trading system');
      }
    } catch (error) {
      addNotification('❌ Error starting trading: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const handleStopTrading = async () => {
    try {
      addNotification('⏹️ Stopping trading system...');

      // Call the correct backend endpoint to stop trading
      const response = await fetch('http://localhost:3205/trading-loop/stop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setAppState(prev => ({ ...prev, isTrading: false, lastUpdate: new Date() }));
        addNotification('✅ Trading system stopped successfully!');
      } else {
        addNotification('❌ Failed to stop trading system');
      }
    } catch (error) {
      addNotification('❌ Error stopping trading: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const checkSystemHealth = async () => {
    try {
      const response = await fetch('http://localhost:3205/health');
      if (response.ok) {
        setAppState(prev => ({ ...prev, systemStatus: 'healthy', lastUpdate: new Date() }));
      } else {
        setAppState(prev => ({ ...prev, systemStatus: 'warning', lastUpdate: new Date() }));
      }
    } catch (error) {
      setAppState(prev => ({ ...prev, systemStatus: 'error', lastUpdate: new Date() }));
    }
  };

  useEffect(() => {
    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <h1>🚀 ApolloX Trading Beast</h1>
          <div className="header-status">
            <div className={`system-status ${appState.systemStatus}`}>
              {appState.systemStatus === 'healthy' && '🟢 System Healthy'}
              {appState.systemStatus === 'warning' && '🟡 System Warning'}
              {appState.systemStatus === 'error' && '🔴 System Error'}
            </div>
            <div className="last-update">
              Updated: {appState.lastUpdate.toLocaleTimeString()}
            </div>
          </div>
        </div>
      </header>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="notifications">
          {notifications.map((notification, index) => (
            <div key={index} className="notification">
              {notification}
            </div>
          ))}
        </div>
      )}

      <main className="app-main">
        <div className="dashboard-grid">
          {/* Trading Control Panel */}
          <div className="dashboard-section">
            <TradingControlPanel
              onStartTrading={handleStartTrading}
              onStopTrading={handleStopTrading}
              isTrading={appState.isTrading}
            />
          </div>

          {/* Live Monitoring Dashboard */}
          <div className="dashboard-section">
            <LiveMonitoringDashboard />
          </div>
        </div>

        {/* Quick Stats */}
        <div className="quick-stats">
          <div className="stat-card">
            <div className="stat-title">🎮 Trading Status</div>
            <div className={`stat-value ${appState.isTrading ? 'active' : 'inactive'}`}>
              {appState.isTrading ? 'ACTIVE' : 'STOPPED'}
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-title">🌐 System Health</div>
            <div className={`stat-value ${appState.systemStatus}`}>
              {appState.systemStatus.toUpperCase()}
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-title">🔗 Quick Links</div>
            <div className="quick-links">
              <a href="http://localhost:3205/static/profit_tracker_apollox.html" target="_blank" rel="noopener noreferrer">
                📊 Profit Tracker
              </a>
              <a href="https://www.apollox.finance/en/futures/v2/BNBUSD" target="_blank" rel="noopener noreferrer">
                🚀 ApolloX
              </a>
            </div>
          </div>
        </div>
      </main>

      <footer className="app-footer">
        <div className="footer-content">
          <div className="footer-section">
            <h4>🎯 Features</h4>
            <ul>
              <li>✅ Real-time profit tracking</li>
              <li>✅ Automated position management</li>
              <li>✅ Multi-tier profit taking</li>
              <li>✅ Smart trailing stops</li>
            </ul>
          </div>

          <div className="footer-section">
            <h4>⚡ Status</h4>
            <ul>
              <li>Backend: {appState.systemStatus === 'healthy' ? '🟢 Online' : '🔴 Offline'}</li>
              <li>Trading: {appState.isTrading ? '🟢 Active' : '🔴 Stopped'}</li>
              <li>Web3: Connected to BSC</li>
              <li>ApolloX: Ready</li>
            </ul>
          </div>

          <div className="footer-section">
            <h4>🚀 The Beast</h4>
            <p>User-friendly AND powerful! 🔥</p>
            <p>Real trading, real profits, real automation.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default App;
