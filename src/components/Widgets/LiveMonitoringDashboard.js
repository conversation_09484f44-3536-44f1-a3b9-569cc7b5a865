import React, { useState, useEffect } from 'react';
import './LiveMonitoringDashboard.css';

const LiveMonitoringDashboard = () => {
  const [profitData, setProfitData] = useState(null);
  const [balanceData, setBalanceData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  const fetchData = async () => {
    try {
      setError(null);

      // Fetch profit data
      const profitResponse = await fetch('http://localhost:3205/profit');
      if (!profitResponse.ok) throw new Error('Failed to fetch profit data');
      const profitResult = await profitResponse.json();

      // Fetch balance data
      const balanceResponse = await fetch('http://localhost:3205/balance');
      if (!balanceResponse.ok) throw new Error('Failed to fetch balance data');
      const balanceResult = await balanceResponse.json();

      setProfitData(profitResult);
      setBalanceData(balanceResult);
      setLastUpdate(new Date());
      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="live-monitoring-dashboard">
        <div className="loading">🔄 Loading live data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="live-monitoring-dashboard">
        <div className="error">❌ Error: {error}</div>
        <button onClick={fetchData} className="retry-button">🔄 Retry</button>
      </div>
    );
  }

  return (
    <div className="live-monitoring-dashboard">
      <div className="dashboard-header">
        <h2>📊 Live Trading Monitor</h2>
        <div className="last-update">
          Last updated: {lastUpdate.toLocaleTimeString()}
        </div>
      </div>

      {/* Status Cards */}
      <div className="status-cards">
        <div className="status-card profit">
          <div className="card-title">💰 Total Profit</div>
          <div className="card-value">
            ${parseFloat(profitData?.total_profit || '0').toFixed(2)}
          </div>
          <div className="card-subtitle">
            {parseFloat(profitData?.profit_percentage || '0').toFixed(2)}%
          </div>
        </div>

        <div className="status-card portfolio">
          <div className="card-title">💼 Portfolio Value</div>
          <div className="card-value">
            ${parseFloat(balanceData?.total_usd || '0').toFixed(2)}
          </div>
          <div className="card-subtitle">Real-time balance</div>
        </div>

        <div className="status-card trades">
          <div className="card-title">📈 Total Trades</div>
          <div className="card-value">
            {profitData?.trading_stats?.total_trades || 0}
          </div>
          <div className="card-subtitle">
            {profitData?.trading_stats?.open_positions || 0} open
          </div>
        </div>

        <div className="status-card pnl">
          <div className="card-title">⚡ Unrealized P&L</div>
          <div className="card-value">
            ${parseFloat(profitData?.trading_stats?.unrealized_profit || '0').toFixed(2)}
          </div>
          <div className="card-subtitle">Live positions</div>
        </div>
      </div>

      {/* Recent Trades */}
      <div className="recent-trades">
        <h3>🔥 Recent Trades</h3>
        <div className="trades-container">
          {profitData?.trades?.slice(0, 10).map((trade, index) => (
            <div key={trade.id || index} className={`trade-item ${trade.side?.toLowerCase() || ''} ${trade.is_live_apollox ? 'live-apollox' : ''}`}>
              <div className="trade-header">
                <span className="trade-symbol">{trade.symbol}</span>
                <span className={`trade-side ${trade.side?.toLowerCase() || ''}`}>
                  {trade.side || 'N/A'}
                </span>
                {trade.is_live_apollox && (
                  <span className="live-badge">🚀 LIVE</span>
                )}
              </div>
              <div className="trade-details">
                <div className="trade-detail">
                  <span className="label">Amount:</span>
                  <span className="value">{parseFloat(trade.amount || '0').toFixed(4)}</span>
                </div>
                <div className="trade-detail">
                  <span className="label">Price:</span>
                  <span className="value">${parseFloat(trade.price || '0').toFixed(4)}</span>
                </div>
                <div className="trade-detail">
                  <span className="label">Value:</span>
                  <span className="value">${parseFloat(trade.trade_value || '0').toFixed(2)}</span>
                </div>
                <div className="trade-detail">
                  <span className="label">P&L:</span>
                  <span className={`value ${((parseFloat(trade.realized_pnl) || 0) + (parseFloat(trade.unrealized_pnl) || 0)) >= 0 ? 'profit' : 'loss'}`}>
                    ${((parseFloat(trade.realized_pnl) || 0) + (parseFloat(trade.unrealized_pnl) || 0)).toFixed(2)}
                  </span>
                </div>
              </div>
              <div className="trade-timestamp">
                {trade.timestamp ? new Date(trade.timestamp).toLocaleString() : 'N/A'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Balance Breakdown */}
      <div className="balance-breakdown">
        <h3>💰 Wallet Balance</h3>
        <div className="balance-items">
          {balanceData && Object.entries(balanceData.balances || {}).map(([token, info]) => (
            <div key={token} className="balance-item">
              <div className="token-symbol">{token}</div>
              <div className="token-amount">{parseFloat(info.balance || '0').toFixed(6)}</div>
              <div className="token-value">${parseFloat(info.usd_value || '0').toFixed(2)}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LiveMonitoringDashboard;
