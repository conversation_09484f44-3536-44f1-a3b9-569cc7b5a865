.live-monitoring-dashboard {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 15px;
  padding: 25px;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  margin-bottom: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  border-bottom: 2px solid #ffd700;
  padding-bottom: 15px;
}

.dashboard-header h2 {
  margin: 0;
  color: #ffd700;
  font-size: 1.8rem;
  font-weight: bold;
}

.last-update {
  color: #888;
  font-size: 0.9rem;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  font-size: 1.2rem;
}

.error {
  color: #ff6b6b;
}

.retry-button {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 15px;
  font-size: 1rem;
}

.retry-button:hover {
  background: #45a049;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.status-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(255, 215, 0, 0.3);
}

.status-card.profit {
  border-color: #4CAF50;
}

.status-card.portfolio {
  border-color: #2196F3;
}

.status-card.trades {
  border-color: #FF9800;
}

.status-card.pnl {
  border-color: #9C27B0;
}

.card-title {
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 10px;
}

.card-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #ffd700;
  margin-bottom: 5px;
}

.card-subtitle {
  font-size: 0.8rem;
  color: #888;
}

.recent-trades {
  margin-bottom: 30px;
}

.recent-trades h3 {
  color: #ffd700;
  margin-bottom: 20px;
  font-size: 1.4rem;
}

.trades-container {
  display: grid;
  gap: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.trade-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  border-left: 4px solid #4CAF50;
  transition: all 0.3s ease;
}

.trade-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.trade-item.sell {
  border-left-color: #f44336;
}

.trade-item.live-apollox {
  border: 2px solid #ffd700;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  animation: pulse-gold 2s infinite;
}

@keyframes pulse-gold {
  0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
  50% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.6); }
  100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
}

.live-badge {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: 10px;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.7; }
}

.trade-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.trade-symbol {
  font-weight: bold;
  font-size: 1.1rem;
  color: #ffd700;
}

.trade-side {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.trade-side.buy {
  background: #4CAF50;
  color: white;
}

.trade-side.sell {
  background: #f44336;
  color: white;
}

.trade-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 10px;
}

.trade-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trade-detail .label {
  color: #ccc;
  font-size: 0.9rem;
}

.trade-detail .value {
  color: white;
  font-weight: bold;
}

.trade-detail .value.profit {
  color: #4CAF50;
}

.trade-detail .value.loss {
  color: #f44336;
}

.trade-timestamp {
  font-size: 0.8rem;
  color: #888;
  text-align: right;
}

.balance-breakdown h3 {
  color: #ffd700;
  margin-bottom: 20px;
  font-size: 1.4rem;
}

.balance-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.balance-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.token-symbol {
  font-weight: bold;
  color: #ffd700;
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.token-amount {
  color: white;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.token-value {
  color: #4CAF50;
  font-weight: bold;
  font-size: 1rem;
}

/* Scrollbar styling */
.trades-container::-webkit-scrollbar {
  width: 8px;
}

.trades-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.trades-container::-webkit-scrollbar-thumb {
  background: #ffd700;
  border-radius: 4px;
}

.trades-container::-webkit-scrollbar-thumb:hover {
  background: #ffed4e;
}
