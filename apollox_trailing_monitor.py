#!/usr/bin/env python3
"""
🤖 APOLLOX TRAILING STOP MONITOR 🤖
Monitors your +$15.28 ApolloX position and applies trailing stops!
"""
import asyncio
import aiohttp
import json
from datetime import datetime
import time

# 🎯 CONFIGURATION FOR YOUR +$15.28 APOLLOX POSITION
API_URL = "http://localhost:3205"
PROFIT_TARGET_PCT = 1.0      # Take profit at 1% gain
TRAILING_STOP_PCT = 0.5      # Trail by 0.5%
CHECK_INTERVAL = 10          # Check every 10 seconds
MAX_LOSS_PCT = 2.0           # Stop loss at 2% loss

# 🎯 TRACKING VARIABLES
highest_profit = 0.0
trailing_stop_price = 0.0
position_monitored = False

async def get_apollox_position():
    """Get your live ApolloX position data"""
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{API_URL}/profit") as response:
                data = await response.json()
                
                # Look for live ApolloX trades
                for trade in data.get("trades", []):
                    if trade.get("is_live_apollox"):
                        return trade
                        
                return None
        except Exception as e:
            print(f"❌ Error getting ApolloX position: {e}")
            return None

async def monitor_trailing_stops():
    """Main monitoring loop for trailing stops"""
    global highest_profit, trailing_stop_price, position_monitored
    
    print("🤖 APOLLOX TRAILING STOP MONITOR STARTED!")
    print(f"🎯 Monitoring your +$15.28 ApolloX position")
    print(f"📈 Profit target: {PROFIT_TARGET_PCT}%")
    print(f"📉 Trailing stop: {TRAILING_STOP_PCT}%")
    print(f"🛑 Max loss: {MAX_LOSS_PCT}%")
    print("=" * 60)
    
    while True:
        try:
            position = await get_apollox_position()
            
            if not position:
                if position_monitored:
                    print("❌ ApolloX position not found - may have been closed")
                    position_monitored = False
                else:
                    print("⏳ Waiting for ApolloX position...")
                await asyncio.sleep(CHECK_INTERVAL)
                continue
            
            # Extract position data
            symbol = position.get("symbol", "UNKNOWN")
            side = position.get("side", "UNKNOWN")
            current_pnl = float(position.get("unrealized_pnl", 0))
            entry_price = float(position.get("price", 0))
            current_price = float(position.get("current_price", entry_price))
            
            # Calculate profit percentage
            if entry_price > 0:
                if side == "LONG":
                    profit_pct = ((current_price - entry_price) / entry_price) * 100
                else:  # SHORT
                    profit_pct = ((entry_price - current_price) / entry_price) * 100
            else:
                profit_pct = 0
            
            position_monitored = True
            
            # Update highest profit for trailing
            if profit_pct > highest_profit:
                highest_profit = profit_pct
                # Set trailing stop price
                if side == "LONG":
                    trailing_stop_price = current_price * (1 - TRAILING_STOP_PCT / 100)
                else:  # SHORT
                    trailing_stop_price = current_price * (1 + TRAILING_STOP_PCT / 100)
                
                print(f"🚀 NEW HIGH! {symbol} {side}: {profit_pct:.2f}% (${current_pnl:.2f})")
                print(f"📈 Trailing stop updated: ${trailing_stop_price:.4f}")
            
            # Check profit target
            if profit_pct >= PROFIT_TARGET_PCT:
                print(f"\n🎯 PROFIT TARGET HIT! {profit_pct:.2f}% >= {PROFIT_TARGET_PCT}%")
                print(f"💰 Position: {symbol} {side} - ${current_pnl:.2f} profit!")
                print(f"🔔 RECOMMENDATION: Consider taking profit manually on ApolloX")
                print(f"🌐 Go to: https://www.apollox.finance/en/futures/v2/{symbol}")
                print("=" * 60)
            
            # Check trailing stop
            elif highest_profit > 0 and trailing_stop_price > 0:
                stop_triggered = False
                
                if side == "LONG" and current_price <= trailing_stop_price:
                    stop_triggered = True
                elif side == "SHORT" and current_price >= trailing_stop_price:
                    stop_triggered = True
                
                if stop_triggered:
                    print(f"\n🛑 TRAILING STOP TRIGGERED!")
                    print(f"📉 {symbol} {side}: Price ${current_price:.4f} hit stop ${trailing_stop_price:.4f}")
                    print(f"💰 Profit secured: {highest_profit:.2f}% (${current_pnl:.2f})")
                    print(f"🔔 RECOMMENDATION: Close position manually on ApolloX")
                    print(f"🌐 Go to: https://www.apollox.finance/en/futures/v2/{symbol}")
                    print("=" * 60)
            
            # Check stop loss
            if profit_pct <= -MAX_LOSS_PCT:
                print(f"\n🚨 STOP LOSS HIT! {profit_pct:.2f}% <= -{MAX_LOSS_PCT}%")
                print(f"💔 Position: {symbol} {side} - ${current_pnl:.2f} loss")
                print(f"🔔 RECOMMENDATION: Close position to limit loss")
                print(f"🌐 Go to: https://www.apollox.finance/en/futures/v2/{symbol}")
                print("=" * 60)
            
            # Regular status update
            else:
                status = "🟢" if profit_pct > 0 else "🔴"
                print(f"{status} {symbol} {side}: {profit_pct:.2f}% (${current_pnl:.2f}) | High: {highest_profit:.2f}%")
            
            await asyncio.sleep(CHECK_INTERVAL)
            
        except Exception as e:
            print(f"❌ Monitor error: {e}")
            await asyncio.sleep(CHECK_INTERVAL * 2)

if __name__ == "__main__":
    print("🚀 STARTING APOLLOX TRAILING STOP MONITOR 🚀")
    print("🎯 This will monitor your +$15.28 ApolloX position")
    print("🤖 Trailing stops will be applied automatically")
    print("=" * 60)
    
    try:
        asyncio.run(monitor_trailing_stops())
    except KeyboardInterrupt:
        print("\n🛑 Monitor stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
