{"version": 3, "file": "static/css/main.6d0f3ab9.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCZA,uBACE,kDAA6D,CAC7D,kBAAmB,CAInB,gCAA0C,CAF1C,UAAY,CACZ,qDAA4D,CAE5D,kBAAmB,CAJnB,YAKF,CAEA,cAGE,kBAAmB,CAEnB,4BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CAEnB,mBACF,CAEA,iBAEE,UAAc,CACd,gBAAiB,CACjB,eAAiB,CAHjB,QAIF,CAEA,kBAEE,kBAAmB,CAEnB,eAAiB,CADjB,eAAiB,CAFjB,gBAIF,CAEA,4BACE,oBAAkC,CAClC,wBAAyB,CACzB,aACF,CAEA,+BACE,oBAAkC,CAClC,wBAAyB,CACzB,aACF,CAEA,SAIE,UAAc,CADd,gBAAiB,CADjB,YAAa,CADb,iBAIF,CAEA,cASE,kBAAmB,CARnB,oBAAkC,CAClC,wBAAyB,CACzB,iBAAkB,CAGlB,aAAc,CACd,YAAa,CACb,6BAA8B,CAH9B,kBAAmB,CADnB,YAMF,CAEA,WACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,eAAiB,CAHjB,gBAIF,CAEA,iBACE,kBACF,CAEA,eACE,kBACF,CAEA,aAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,aACE,oBAAqC,CAIrC,0BAAwC,CAHxC,kBAAmB,CACnB,YAAa,CACb,iBAEF,CAEA,cACE,UAAW,CACX,eAAiB,CACjB,iBACF,CAEA,cACE,UAAc,CAEd,gBAAiB,CADjB,eAEF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,oBACE,UAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,cACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,aAIE,WAAY,CACZ,kBAAmB,CAGnB,cAAe,CAPf,QAAO,CAKP,cAAe,CACf,eAAiB,CALjB,eAAgB,CAChB,iBAAkB,CAOlB,wBAAyB,CADzB,uBAEF,CAEA,mBACE,kDAAqD,CACrD,UACF,CAEA,wCACE,kDAAqD,CAErD,+BAA6C,CAD7C,0BAEF,CAEA,kBACE,kDAAqD,CACrD,UACF,CAEA,uCACE,kDAAqD,CAErD,+BAA6C,CAD7C,0BAEF,CAEA,uBAGE,2BAA4B,CAF5B,kDAAqD,CACrD,UAEF,CAEA,6BACE,kDAAqD,CAErD,+BAA6C,CAD7C,0BAEF,CAEA,mBACE,kDAAqD,CACrD,UACF,CAEA,yBACE,kDAAqD,CAErD,+BAA8C,CAD9C,0BAEF,CAEA,sBAIE,yBAA2B,CAF3B,kBAAmB,CADnB,UAAY,CAEZ,wBAEF,CAEA,iBACE,GACE,4BACF,CACA,IACE,+BACF,CACA,GACE,4BACF,CACF,CAEA,eACE,kBACF,CAEA,kBACE,UAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,gBACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,YAEE,oBAAkC,CAClC,qBAAyB,CACzB,iBAAkB,CAClB,UAAc,CAEd,cAAe,CAGf,oBAAqB,CAJrB,eAAiB,CALjB,iBAAkB,CAQlB,oBAAqB,CADrB,uBAGF,CAEA,kBACE,oBAAkC,CAElC,+BAA6C,CAD7C,0BAEF,CAEA,gBAGE,oBAAqC,CACrC,kBAAmB,CAFnB,YAAa,CADb,iBAIF,CAEA,cAGE,kBAAmB,CAFnB,oBAAqB,CAIrB,gBAAiB,CADjB,eAAiB,CAEjB,kBAAmB,CAJnB,iBAKF,CAEA,qBAIE,sDAAuD,CAHvD,oBAAkC,CAClC,wBAAyB,CACzB,aAEF,CAEA,uBACE,oBAAkC,CAClC,wBAAyB,CACzB,aACF,CAEA,sBACE,GACE,4BACF,CACA,GACE,6BACF,CACF,CAEA,gBACE,UAAW,CACX,eACF,CAEA,oBACE,YACF,CC5RA,2BACE,kDAA6D,CAC7D,kBAAmB,CAInB,gCAA0C,CAF1C,UAAY,CACZ,qDAA4D,CAE5D,kBAAmB,CAJnB,YAKF,CAEA,kBAGE,kBAAmB,CAEnB,4BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CAEnB,mBACF,CAEA,qBAEE,UAAc,CACd,gBAAiB,CACjB,eAAiB,CAHjB,QAIF,CAEA,aAEE,eACF,CAEA,gBAGE,gBAAiB,CADjB,YAAa,CADb,iBAGF,CAEA,OACE,aACF,CAEA,cACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CAEf,cAAe,CADf,eAAgB,CAHhB,iBAKF,CAEA,oBACE,kBACF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,aACE,oBAAoC,CAIpC,sBAA6B,CAH7B,kBAAmB,CACnB,YAAa,CACb,iBAAkB,CAElB,uBACF,CAEA,mBAEE,+BAA6C,CAD7C,0BAEF,CAEA,oBACE,oBACF,CAEA,uBACE,oBACF,CAEA,oBACE,oBACF,CAEA,iBACE,oBACF,CAEA,YAEE,UAAW,CADX,eAAiB,CAEjB,kBACF,CAEA,YAGE,UAAc,CAFd,gBAAiB,CACjB,eAAiB,CAEjB,iBACF,CAEA,eAEE,UAAW,CADX,eAEF,CAEA,eACE,kBACF,CAEA,kBACE,UAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,kBAEE,aAAS,CADT,YAAa,CACb,QAAS,CACT,gBAAiB,CACjB,eACF,CAEA,YACE,oBAAqC,CAGrC,6BAA8B,CAF9B,kBAAmB,CACnB,YAAa,CAEb,uBACF,CAEA,kBACE,oBACF,CAEA,iBACE,yBACF,CAEA,yBAIE,gCAAiC,CAFjC,sDAA4F,CAD5F,qBAAyB,CAEzB,6BAEF,CAEA,sBACE,GAAK,6BAA6C,CAClD,IAAM,6BAA6C,CACnD,GAAO,6BAA6C,CACtD,CAEA,YAQE,6BAA8B,CAP9B,8CAAoD,CAGpD,kBAAmB,CAFnB,UAAW,CAGX,eAAiB,CACjB,eAAiB,CACjB,gBAAiB,CAJjB,eAMF,CAEA,iBACE,OAAU,SAAY,CACtB,OAAY,UAAc,CAC5B,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,cAGE,UAAc,CADd,gBAAiB,CADjB,eAGF,CAEA,YAEE,kBAAmB,CACnB,eAAiB,CACjB,eAAiB,CAHjB,gBAAiB,CAIjB,wBACF,CAEA,gBACE,kBAAmB,CACnB,UACF,CAEA,iBACE,kBAAmB,CACnB,UACF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAEF,CAEA,qBACE,UAAW,CACX,eACF,CAEA,qBACE,UAAY,CACZ,eACF,CAEA,4BACE,aACF,CAEA,0BACE,aACF,CAEA,iBAEE,UAAW,CADX,eAAiB,CAEjB,gBACF,CAEA,sBACE,UAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,cACE,oBAAqC,CAIrC,0BAAwC,CAHxC,kBAAmB,CACnB,YAAa,CACb,iBAEF,CAEA,cAEE,UAAc,CACd,gBAAiB,CAFjB,eAAiB,CAGjB,iBACF,CAEA,cACE,UAAY,CACZ,eAAiB,CACjB,iBACF,CAEA,aACE,aAAc,CAEd,cAAe,CADf,eAEF,CAGA,qCACE,SACF,CAEA,2CACE,oBAAoC,CACpC,iBACF,CAEA,2CACE,eAAmB,CACnB,iBACF,CAEA,iDACE,kBACF,CCzSA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAEE,8DAA0E,CAE1E,UAAY,CAHZ,qDAIF,CAEA,UAJE,gBAQF,CAJA,KAEE,YAAa,CACb,qBACF,CAGA,YAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAA8B,CAE9B,4BAAgC,CAChC,cAAe,CACf,eAAgB,CAChB,KAAM,CACN,WACF,CAEA,gBAME,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAH9B,aAAc,CADd,gBAAiB,CAEjB,cAIF,CAEA,eACE,UAAc,CACd,gBAAiB,CACjB,eAAiB,CACjB,8BACF,CAEA,eAGE,oBAAqB,CAFrB,YAAa,CACb,qBAAsB,CAEtB,OACF,CAEA,eAEE,kBAAmB,CAEnB,eAAiB,CADjB,eAAiB,CAFjB,gBAIF,CAEA,uBACE,oBAAkC,CAClC,wBAAyB,CACzB,aACF,CAEA,uBACE,oBAAkC,CAClC,wBAAyB,CACzB,aACF,CAEA,qBACE,oBAAkC,CAClC,wBAAyB,CACzB,aACF,CAEA,aACE,UAAW,CACX,eACF,CAGA,eAKE,YAAa,CACb,qBAAsB,CACtB,QAAS,CANT,cAAe,CAEf,UAAW,CADX,SAAU,CAEV,YAIF,CAEA,cAQE,8BAAgC,CAPhC,oBAA8B,CAC9B,qBAAyB,CACzB,kBAAmB,CAInB,+BAAyC,CAFzC,UAAY,CACZ,eAAiB,CAGjB,eAAgB,CALhB,iBAMF,CAEA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAGA,UACE,QAAO,CAEP,aAAc,CADd,gBAAiB,CAEjB,iBAAkB,CAClB,UACF,CAEA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yBAA0B,CAE1B,kBACF,CAEA,0BACE,gBACE,6BACF,CACF,CAEA,mBACE,gBACF,CAGA,aAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,WACE,oBAAqC,CAGrC,0BAAwC,CAFxC,kBAAmB,CACnB,YAAa,CAEb,uBACF,CAEA,iBAEE,gCAA8C,CAD9C,0BAEF,CAEA,YACE,UAAc,CACd,cAGF,CAEA,wBAJE,eAAiB,CACjB,kBAOF,CAJA,YACE,gBAGF,CAEA,mBACE,aAAc,CACd,8BACF,CAEA,qBACE,aACF,CAEA,oBACE,aACF,CAEA,oBACE,aACF,CAEA,kBACE,aACF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,eACE,UAAc,CAEd,eAAiB,CADjB,oBAAqB,CAErB,yBACF,CAEA,qBACE,aAAc,CACd,6BACF,CAGA,YACE,oBAA8B,CAC9B,yBAA6B,CAE7B,eAAgB,CADhB,cAEF,CAEA,gBAME,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAH3D,aAAc,CADd,gBAAiB,CAEjB,cAIF,CAEA,mBACE,UAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,mBACE,eACF,CAEA,mBAEE,UAAW,CACX,eAAiB,CAFjB,iBAGF,CAEA,kBACE,UAAW,CACX,eAAgB,CAChB,kBACF,CAGA,yBACE,eACE,gBACF,CAEA,gBACE,qBAAsB,CACtB,QAAS,CACT,iBACF,CAEA,eACE,kBACF,CAEA,eAEE,SAAU,CADV,UAEF,CAEA,cACE,cACF,CAEA,UACE,iBACF,CAUA,6CAHE,yBAMF,CAHA,gBAEE,iBACF,CACF,CAGA,oBACE,UACF,CAEA,0BACE,oBACF,CAEA,0BACE,eAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,SACE,iCACF", "sources": ["index.css", "components/Widgets/TradingControlPanel.css", "components/Widgets/LiveMonitoringDashboard.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", ".trading-control-panel {\n  background: linear-gradient(135deg, #2c1810 0%, #1a1a2e 100%);\n  border-radius: 15px;\n  padding: 25px;\n  color: white;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  margin-bottom: 20px;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25px;\n  border-bottom: 2px solid #ffd700;\n  padding-bottom: 15px;\n}\n\n.panel-header h2 {\n  margin: 0;\n  color: #ffd700;\n  font-size: 1.8rem;\n  font-weight: bold;\n}\n\n.status-indicator {\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-weight: bold;\n  font-size: 0.9rem;\n}\n\n.status-indicator.connected {\n  background: rgba(76, 175, 80, 0.2);\n  border: 2px solid #4CAF50;\n  color: #4CAF50;\n}\n\n.status-indicator.disconnected {\n  background: rgba(244, 67, 54, 0.2);\n  border: 2px solid #f44336;\n  color: #f44336;\n}\n\n.loading {\n  text-align: center;\n  padding: 40px;\n  font-size: 1.2rem;\n  color: #ffd700;\n}\n\n.error-banner {\n  background: rgba(244, 67, 54, 0.1);\n  border: 1px solid #f44336;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 20px;\n  color: #f44336;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.retry-btn {\n  background: #f44336;\n  color: white;\n  border: none;\n  padding: 5px 10px;\n  border-radius: 5px;\n  cursor: pointer;\n  font-size: 0.8rem;\n}\n\n.retry-btn:hover {\n  background: #d32f2f;\n}\n\n.system-status {\n  margin-bottom: 30px;\n}\n\n.status-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 15px;\n}\n\n.status-item {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  padding: 15px;\n  text-align: center;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n}\n\n.status-label {\n  color: #ccc;\n  font-size: 0.9rem;\n  margin-bottom: 8px;\n}\n\n.status-value {\n  color: #ffd700;\n  font-weight: bold;\n  font-size: 1.1rem;\n}\n\n.main-controls {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 25px;\n  margin-bottom: 30px;\n}\n\n.control-section h3 {\n  color: #ffd700;\n  margin-bottom: 15px;\n  font-size: 1.2rem;\n}\n\n.button-group {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.control-btn {\n  flex: 1;\n  min-width: 140px;\n  padding: 15px 20px;\n  border: none;\n  border-radius: 10px;\n  font-size: 1rem;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-transform: uppercase;\n}\n\n.control-btn.start {\n  background: linear-gradient(135deg, #4CAF50, #45a049);\n  color: white;\n}\n\n.control-btn.start:hover:not(.disabled) {\n  background: linear-gradient(135deg, #45a049, #3d8b40);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);\n}\n\n.control-btn.stop {\n  background: linear-gradient(135deg, #f44336, #d32f2f);\n  color: white;\n}\n\n.control-btn.stop:hover:not(.disabled) {\n  background: linear-gradient(135deg, #d32f2f, #b71c1c);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);\n}\n\n.control-btn.emergency {\n  background: linear-gradient(135deg, #ff5722, #d84315);\n  color: white;\n  animation: pulse 2s infinite;\n}\n\n.control-btn.emergency:hover {\n  background: linear-gradient(135deg, #d84315, #bf360c);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(255, 87, 34, 0.4);\n}\n\n.control-btn.clear {\n  background: linear-gradient(135deg, #9C27B0, #7B1FA2);\n  color: white;\n}\n\n.control-btn.clear:hover {\n  background: linear-gradient(135deg, #7B1FA2, #6A1B9A);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(156, 39, 176, 0.4);\n}\n\n.control-btn.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none !important;\n  box-shadow: none !important;\n}\n\n@keyframes pulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.7);\n  }\n  70% {\n    box-shadow: 0 0 0 10px rgba(255, 87, 34, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(255, 87, 34, 0);\n  }\n}\n\n.quick-actions {\n  margin-bottom: 30px;\n}\n\n.quick-actions h3 {\n  color: #ffd700;\n  margin-bottom: 15px;\n  font-size: 1.2rem;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.action-btn {\n  padding: 12px 18px;\n  background: rgba(255, 215, 0, 0.1);\n  border: 2px solid #ffd700;\n  border-radius: 8px;\n  color: #ffd700;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-decoration: none;\n  display: inline-block;\n}\n\n.action-btn:hover {\n  background: rgba(255, 215, 0, 0.2);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);\n}\n\n.trading-status {\n  text-align: center;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n}\n\n.status-badge {\n  display: inline-block;\n  padding: 10px 20px;\n  border-radius: 25px;\n  font-weight: bold;\n  font-size: 1.1rem;\n  margin-bottom: 15px;\n}\n\n.status-badge.active {\n  background: rgba(76, 175, 80, 0.2);\n  border: 2px solid #4CAF50;\n  color: #4CAF50;\n  animation: glow-green 2s ease-in-out infinite alternate;\n}\n\n.status-badge.inactive {\n  background: rgba(244, 67, 54, 0.2);\n  border: 2px solid #f44336;\n  color: #f44336;\n}\n\n@keyframes glow-green {\n  from {\n    box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);\n  }\n  to {\n    box-shadow: 0 0 20px rgba(76, 175, 80, 0.8);\n  }\n}\n\n.status-details {\n  color: #ccc;\n  font-size: 0.9rem;\n}\n\n.status-details div {\n  margin: 5px 0;\n}\n", ".live-monitoring-dashboard {\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\n  border-radius: 15px;\n  padding: 25px;\n  color: white;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  margin-bottom: 20px;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25px;\n  border-bottom: 2px solid #ffd700;\n  padding-bottom: 15px;\n}\n\n.dashboard-header h2 {\n  margin: 0;\n  color: #ffd700;\n  font-size: 1.8rem;\n  font-weight: bold;\n}\n\n.last-update {\n  color: #888;\n  font-size: 0.9rem;\n}\n\n.loading, .error {\n  text-align: center;\n  padding: 40px;\n  font-size: 1.2rem;\n}\n\n.error {\n  color: #ff6b6b;\n}\n\n.retry-button {\n  background: #4CAF50;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n  margin-top: 15px;\n  font-size: 1rem;\n}\n\n.retry-button:hover {\n  background: #45a049;\n}\n\n.status-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.status-card {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 20px;\n  text-align: center;\n  border: 2px solid transparent;\n  transition: all 0.3s ease;\n}\n\n.status-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 5px 20px rgba(255, 215, 0, 0.3);\n}\n\n.status-card.profit {\n  border-color: #4CAF50;\n}\n\n.status-card.portfolio {\n  border-color: #2196F3;\n}\n\n.status-card.trades {\n  border-color: #FF9800;\n}\n\n.status-card.pnl {\n  border-color: #9C27B0;\n}\n\n.card-title {\n  font-size: 0.9rem;\n  color: #ccc;\n  margin-bottom: 10px;\n}\n\n.card-value {\n  font-size: 1.8rem;\n  font-weight: bold;\n  color: #ffd700;\n  margin-bottom: 5px;\n}\n\n.card-subtitle {\n  font-size: 0.8rem;\n  color: #888;\n}\n\n.recent-trades {\n  margin-bottom: 30px;\n}\n\n.recent-trades h3 {\n  color: #ffd700;\n  margin-bottom: 20px;\n  font-size: 1.4rem;\n}\n\n.trades-container {\n  display: grid;\n  gap: 15px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.trade-item {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  padding: 15px;\n  border-left: 4px solid #4CAF50;\n  transition: all 0.3s ease;\n}\n\n.trade-item:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.trade-item.sell {\n  border-left-color: #f44336;\n}\n\n.trade-item.live-apollox {\n  border: 2px solid #ffd700;\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);\n  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);\n  animation: pulse-gold 2s infinite;\n}\n\n@keyframes pulse-gold {\n  0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }\n  50% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.6); }\n  100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }\n}\n\n.live-badge {\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  color: #000;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: bold;\n  margin-left: 10px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.7; }\n}\n\n.trade-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.trade-symbol {\n  font-weight: bold;\n  font-size: 1.1rem;\n  color: #ffd700;\n}\n\n.trade-side {\n  padding: 4px 12px;\n  border-radius: 15px;\n  font-size: 0.8rem;\n  font-weight: bold;\n  text-transform: uppercase;\n}\n\n.trade-side.buy {\n  background: #4CAF50;\n  color: white;\n}\n\n.trade-side.sell {\n  background: #f44336;\n  color: white;\n}\n\n.trade-details {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 10px;\n  margin-bottom: 10px;\n}\n\n.trade-detail {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.trade-detail .label {\n  color: #ccc;\n  font-size: 0.9rem;\n}\n\n.trade-detail .value {\n  color: white;\n  font-weight: bold;\n}\n\n.trade-detail .value.profit {\n  color: #4CAF50;\n}\n\n.trade-detail .value.loss {\n  color: #f44336;\n}\n\n.trade-timestamp {\n  font-size: 0.8rem;\n  color: #888;\n  text-align: right;\n}\n\n.balance-breakdown h3 {\n  color: #ffd700;\n  margin-bottom: 20px;\n  font-size: 1.4rem;\n}\n\n.balance-items {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 15px;\n}\n\n.balance-item {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  padding: 15px;\n  text-align: center;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n}\n\n.token-symbol {\n  font-weight: bold;\n  color: #ffd700;\n  font-size: 1.1rem;\n  margin-bottom: 5px;\n}\n\n.token-amount {\n  color: white;\n  font-size: 0.9rem;\n  margin-bottom: 5px;\n}\n\n.token-value {\n  color: #4CAF50;\n  font-weight: bold;\n  font-size: 1rem;\n}\n\n/* Scrollbar styling */\n.trades-container::-webkit-scrollbar {\n  width: 8px;\n}\n\n.trades-container::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n.trades-container::-webkit-scrollbar-thumb {\n  background: #ffd700;\n  border-radius: 4px;\n}\n\n.trades-container::-webkit-scrollbar-thumb:hover {\n  background: #ffed4e;\n}\n", "* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  min-height: 100vh;\n  color: white;\n}\n\n.app {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Header */\n.app-header {\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(10px);\n  border-bottom: 2px solid #ffd700;\n  padding: 20px 0;\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.app-header h1 {\n  color: #ffd700;\n  font-size: 2.5rem;\n  font-weight: bold;\n  text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);\n}\n\n.header-status {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 5px;\n}\n\n.system-status {\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-weight: bold;\n  font-size: 0.9rem;\n}\n\n.system-status.healthy {\n  background: rgba(76, 175, 80, 0.2);\n  border: 2px solid #4CAF50;\n  color: #4CAF50;\n}\n\n.system-status.warning {\n  background: rgba(255, 193, 7, 0.2);\n  border: 2px solid #FFC107;\n  color: #FFC107;\n}\n\n.system-status.error {\n  background: rgba(244, 67, 54, 0.2);\n  border: 2px solid #f44336;\n  color: #f44336;\n}\n\n.last-update {\n  color: #888;\n  font-size: 0.8rem;\n}\n\n/* Notifications */\n.notifications {\n  position: fixed;\n  top: 100px;\n  right: 20px;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.notification {\n  background: rgba(0, 0, 0, 0.9);\n  border: 2px solid #ffd700;\n  border-radius: 10px;\n  padding: 15px 20px;\n  color: white;\n  font-weight: bold;\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);\n  animation: slideIn 0.3s ease-out;\n  max-width: 300px;\n}\n\n@keyframes slideIn {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n/* Main Content */\n.app-main {\n  flex: 1;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 30px 20px;\n  width: 100%;\n}\n\n.dashboard-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 30px;\n  margin-bottom: 30px;\n}\n\n@media (min-width: 1024px) {\n  .dashboard-grid {\n    grid-template-columns: 1fr 1fr;\n  }\n}\n\n.dashboard-section {\n  min-height: 400px;\n}\n\n/* Quick Stats */\n.quick-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 20px;\n  border: 2px solid rgba(255, 215, 0, 0.3);\n  transition: all 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);\n}\n\n.stat-title {\n  color: #ffd700;\n  font-size: 1rem;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n.stat-value.active {\n  color: #4CAF50;\n  text-shadow: 0 0 10px rgba(76, 175, 80, 0.5);\n}\n\n.stat-value.inactive {\n  color: #f44336;\n}\n\n.stat-value.healthy {\n  color: #4CAF50;\n}\n\n.stat-value.warning {\n  color: #FFC107;\n}\n\n.stat-value.error {\n  color: #f44336;\n}\n\n.quick-links {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.quick-links a {\n  color: #ffd700;\n  text-decoration: none;\n  font-size: 0.9rem;\n  transition: color 0.3s ease;\n}\n\n.quick-links a:hover {\n  color: #ffed4e;\n  text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);\n}\n\n/* Footer */\n.app-footer {\n  background: rgba(0, 0, 0, 0.3);\n  border-top: 2px solid #ffd700;\n  padding: 30px 0;\n  margin-top: auto;\n}\n\n.footer-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 30px;\n}\n\n.footer-section h4 {\n  color: #ffd700;\n  margin-bottom: 15px;\n  font-size: 1.1rem;\n}\n\n.footer-section ul {\n  list-style: none;\n}\n\n.footer-section li {\n  margin-bottom: 8px;\n  color: #ccc;\n  font-size: 0.9rem;\n}\n\n.footer-section p {\n  color: #ccc;\n  line-height: 1.5;\n  margin-bottom: 10px;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .app-header h1 {\n    font-size: 1.8rem;\n  }\n  \n  .header-content {\n    flex-direction: column;\n    gap: 15px;\n    text-align: center;\n  }\n  \n  .header-status {\n    align-items: center;\n  }\n  \n  .notifications {\n    right: 10px;\n    left: 10px;\n  }\n  \n  .notification {\n    max-width: none;\n  }\n  \n  .app-main {\n    padding: 20px 10px;\n  }\n  \n  .dashboard-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .quick-stats {\n    grid-template-columns: 1fr;\n  }\n  \n  .footer-content {\n    grid-template-columns: 1fr;\n    text-align: center;\n  }\n}\n\n/* Scrollbar Styling */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n::-webkit-scrollbar-thumb {\n  background: #ffd700;\n  border-radius: 6px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #ffed4e;\n}\n\n/* Loading Animation */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading {\n  animation: spin 1s linear infinite;\n}\n"], "names": [], "sourceRoot": ""}